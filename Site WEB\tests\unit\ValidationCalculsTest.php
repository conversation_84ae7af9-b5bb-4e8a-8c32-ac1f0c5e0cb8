<?php
/**
 * Tests de Validation Complète des Calculs
 * Vérifie l'exactitude mathématique et physique de tous les calculs du système
 */

require_once(__DIR__ . '/../../lib/rendement.php');
require_once(__DIR__ . '/../../lib/data_generator.php');
require_once(__DIR__ . '/../../lib/courbes.php');

class ValidationCalculsTest
{
    private $errors = [];
    private $passed = 0;
    private $total = 0;

    public function runAllTests()
    {
        echo "<h2>🧪 Tests de Validation Complète des Calculs</h2>";
        
        $this->testRendementFormulas();
        $this->testUnitConversions();
        $this->testPhysicalLimits();
        $this->testDataGeneratorConsistency();
        $this->testCourbeCalculations();
        $this->testEdgeCases();
        
        $this->displayResults();
    }

    /**
     * Test des formules de rendement
     */
    private function testRendementFormulas()
    {
        echo "<h3>📐 Test des Formules de Rendement</h3>";

        // Test 1: Rendement volumétrique = 100% quand débit réel = débit théorique
        $this->testRendementVolumetrique();
        
        // Test 2: Rendement mécanique limité à 100%
        $this->testRendementMecanique();
        
        // Test 3: Rendement global = volumétrique × mécanique / 100
        $this->testRendementGlobal();
    }

    private function testRendementVolumetrique()
    {
        // Simuler des paramètres d'essai avec débit théorique = débit réel
        $debit_reel = 10.0; // L/min
        $debit_theorique = 10.0; // L/min
        
        $parametres_theoriques = [
            'debit_nominal' => $debit_theorique . ' L/min',
            'puissance' => '100 W'
        ];
        
        $essai_mock = [
            'parametre_theorique' => json_encode($parametres_theoriques)
        ];

        // Utiliser la méthode privée via réflexion pour tester
        $reflection = new ReflectionClass('Rendement');
        $method = $reflection->getMethod('calculerRendements');
        $method->setAccessible(true);
        
        $result = $method->invoke(null, 100000, 100000, $debit_reel, $essai_mock);
        
        $this->assert(
            abs($result['rendement_volumetrique'] - 100.0) < 0.01,
            "Rendement volumétrique devrait être 100% quand débit réel = théorique",
            "Attendu: 100%, Obtenu: " . $result['rendement_volumetrique'] . "%"
        );
    }

    private function testRendementMecanique()
    {
        // Test avec puissance hydraulique > puissance mécanique (cas impossible physiquement)
        $parametres_theoriques = [
            'debit_nominal' => '10 L/min',
            'puissance' => '50 W' // Puissance mécanique plus faible
        ];
        
        $essai_mock = [
            'parametre_theorique' => json_encode($parametres_theoriques)
        ];

        $reflection = new ReflectionClass('Rendement');
        $method = $reflection->getMethod('calculerRendements');
        $method->setAccessible(true);
        
        // Simuler une puissance hydraulique élevée (différence de pression importante)
        $result = $method->invoke(null, 200000, 100000, 10, $essai_mock);
        
        $this->assert(
            $result['rendement_mecanique'] <= 100.0,
            "Rendement mécanique ne doit jamais dépasser 100%",
            "Obtenu: " . $result['rendement_mecanique'] . "%"
        );
    }

    private function testRendementGlobal()
    {
        // Test de la formule : rendement global = volumétrique × mécanique / 100
        $parametres_theoriques = [
            'debit_nominal' => '12 L/min', // 20% supérieur au débit réel
            'puissance' => '120 W' // 20% supérieur à la puissance hydraulique
        ];
        
        $essai_mock = [
            'parametre_theorique' => json_encode($parametres_theoriques)
        ];

        $reflection = new ReflectionClass('Rendement');
        $method = $reflection->getMethod('calculerRendements');
        $method->setAccessible(true);
        
        $result = $method->invoke(null, 150000, 100000, 10, $essai_mock);
        
        $expected_global = ($result['rendement_volumetrique'] * $result['rendement_mecanique']) / 100;
        
        $this->assert(
            abs($result['rendement_global'] - $expected_global) < 0.01,
            "Rendement global = volumétrique × mécanique / 100",
            "Attendu: " . round($expected_global, 2) . "%, Obtenu: " . $result['rendement_global'] . "%"
        );
    }

    /**
     * Test des conversions d'unités
     */
    private function testUnitConversions()
    {
        echo "<h3>🔄 Test des Conversions d'Unités</h3>";

        // Test conversion L/min vers m³/s
        $debit_lmin = 60; // L/min
        $debit_m3s = $debit_lmin / 60000; // Conversion utilisée dans le code
        $expected_m3s = 0.001; // 60 L/min = 0.001 m³/s
        
        $this->assert(
            abs($debit_m3s - $expected_m3s) < 0.0001,
            "Conversion L/min vers m³/s correcte",
            "60 L/min = 0.001 m³/s, Obtenu: " . $debit_m3s . " m³/s"
        );

        // Test conversion Pascal vers bar
        $pression_pascal = 100000; // Pa
        $pression_bar = $pression_pascal / 100000; // Conversion
        $expected_bar = 1.0; // 100000 Pa = 1 bar
        
        $this->assert(
            abs($pression_bar - $expected_bar) < 0.0001,
            "Conversion Pascal vers bar correcte",
            "100000 Pa = 1 bar, Obtenu: " . $pression_bar . " bar"
        );
    }

    /**
     * Test des limites physiques
     */
    private function testPhysicalLimits()
    {
        echo "<h3>⚖️ Test des Limites Physiques</h3>";

        // Test que tous les rendements sont entre 0 et 100%
        $parametres_theoriques = [
            'debit_nominal' => '5 L/min', // Débit théorique plus faible (cas extrême)
            'puissance' => '30 W' // Puissance théorique plus faible
        ];
        
        $essai_mock = [
            'parametre_theorique' => json_encode($parametres_theoriques)
        ];

        $reflection = new ReflectionClass('Rendement');
        $method = $reflection->getMethod('calculerRendements');
        $method->setAccessible(true);
        
        $result = $method->invoke(null, 200000, 150000, 10, $essai_mock);
        
        $this->assert(
            $result['rendement_volumetrique'] >= 0 && $result['rendement_volumetrique'] <= 100,
            "Rendement volumétrique dans la plage 0-100%",
            "Obtenu: " . $result['rendement_volumetrique'] . "%"
        );
        
        $this->assert(
            $result['rendement_mecanique'] >= 0 && $result['rendement_mecanique'] <= 100,
            "Rendement mécanique dans la plage 0-100%",
            "Obtenu: " . $result['rendement_mecanique'] . "%"
        );
        
        $this->assert(
            $result['rendement_global'] >= 0 && $result['rendement_global'] <= 100,
            "Rendement global dans la plage 0-100%",
            "Obtenu: " . $result['rendement_global'] . "%"
        );
    }

    /**
     * Test de cohérence du générateur de données
     */
    private function testDataGeneratorConsistency()
    {
        echo "<h3>🔄 Test de Cohérence du Générateur de Données</h3>";

        $generator = new DataGenerator();
        
        // Utiliser la réflexion pour accéder à la méthode privée
        $reflection = new ReflectionClass($generator);
        $method = $reflection->getMethod('generateRealisticProperties');
        $method->setAccessible(true);
        
        // Générer plusieurs jeux de paramètres et vérifier la cohérence
        for ($i = 0; $i < 10; $i++) {
            $params = $method->invoke($generator);
            
            // Extraire les valeurs numériques
            $debit_nominal = floatval($params['debit_nominal']);
            $puissance = floatval($params['puissance']);
            
            $this->assert(
                $debit_nominal > 0 && $debit_nominal <= 30,
                "Débit nominal dans une plage réaliste",
                "Obtenu: " . $debit_nominal . " L/min"
            );
            
            $this->assert(
                $puissance > 0 && $puissance <= 500,
                "Puissance dans une plage réaliste",
                "Obtenu: " . $puissance . " W"
            );
        }
    }

    /**
     * Test des calculs de courbes
     */
    private function testCourbeCalculations()
    {
        echo "<h3>📈 Test des Calculs de Courbes</h3>";

        // Test du calcul de moyenne
        $donnees_test = [
            ['pressure_pascal' => 100000],
            ['pressure_pascal' => 200000],
            ['pressure_pascal' => 300000]
        ];
        
        $reflection = new ReflectionClass('Courbe');
        $method = $reflection->getMethod('calculateAverageMeasure');
        $method->setAccessible(true);
        
        $moyenne = $method->invoke(null, json_encode($donnees_test));
        $expected = 200000; // (100000 + 200000 + 300000) / 3
        
        $this->assert(
            abs($moyenne - $expected) < 0.01,
            "Calcul de moyenne de pression correct",
            "Attendu: " . $expected . " Pa, Obtenu: " . $moyenne . " Pa"
        );
    }

    /**
     * Test des cas limites
     */
    private function testEdgeCases()
    {
        echo "<h3>🚨 Test des Cas Limites</h3>";

        // Test avec valeurs nulles
        $parametres_theoriques = [
            'debit_nominal' => '0 L/min',
            'puissance' => '0 W'
        ];
        
        $essai_mock = [
            'parametre_theorique' => json_encode($parametres_theoriques)
        ];

        $reflection = new ReflectionClass('Rendement');
        $method = $reflection->getMethod('calculerRendements');
        $method->setAccessible(true);
        
        $result = $method->invoke(null, 0, 0, 0, $essai_mock);
        
        $this->assert(
            $result['rendement_volumetrique'] == 0,
            "Gestion correcte des valeurs nulles",
            "Rendement volumétrique avec débit nul devrait être 0%"
        );
    }

    /**
     * Méthode d'assertion
     */
    private function assert($condition, $message, $details = "")
    {
        $this->total++;
        if ($condition) {
            $this->passed++;
            echo "<div style='color: green;'>✅ $message</div>";
        } else {
            $this->errors[] = $message . ($details ? " - $details" : "");
            echo "<div style='color: red;'>❌ $message" . ($details ? " - $details" : "") . "</div>";
        }
    }

    /**
     * Afficher les résultats
     */
    private function displayResults()
    {
        echo "<h3>📊 Résultats des Tests</h3>";
        echo "<p><strong>Tests passés:</strong> {$this->passed}/{$this->total}</p>";
        
        if (empty($this->errors)) {
            echo "<div style='color: green; font-weight: bold;'>🎉 Tous les tests sont passés avec succès!</div>";
        } else {
            echo "<div style='color: red; font-weight: bold;'>⚠️ Erreurs détectées:</div>";
            foreach ($this->errors as $error) {
                echo "<div style='color: red;'>• $error</div>";
            }
        }
        
        $percentage = $this->total > 0 ? round(($this->passed / $this->total) * 100, 1) : 0;
        echo "<p><strong>Taux de réussite:</strong> $percentage%</p>";
    }
}

// Exécuter les tests si le fichier est appelé directement
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    $test = new ValidationCalculsTest();
    $test->runAllTests();
}
?>
