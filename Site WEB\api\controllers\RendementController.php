<?php
require_once __DIR__ . '/../core/BaseController.php';
require_once __DIR__ . '/../../lib/rendement.php';

class RendementController extends BaseController
{
    public function handle()
    {
        switch ($this->method) {
            case 'GET':
                return $this->index();
            case 'POST':
                return $this->calculate();
            case 'DELETE':
                return $this->delete();
            default:
                $this->error('Méthode non autorisée', 405);
        }
    }

    /**
     * Récupérer les rendements
     */
    private function index()
    {
        try {
            // Si un essai_id est fourni, récupérer le rendement de cet essai
            if (isset($this->data['essai_id'])) {
                $rendement = Rendement::getByEssaiId($this->data['essai_id']);
                if (!$rendement) {
                    return $this->error('Aucun rendement calculé pour cet essai', 404);
                }
                return $this->json($rendement);
            }

            // Si un affaire_id est fourni, récupérer les statistiques
            if (isset($this->data['affaire_id'])) {
                $stats = Rendement::getStatistiquesAffaire($this->data['affaire_id']);
                return $this->json($stats);
            }

            // Sinon, récupérer tous les rendements avec pagination
            $limit = isset($this->data['limit']) ? (int)$this->data['limit'] : 50;
            $offset = isset($this->data['offset']) ? (int)$this->data['offset'] : 0;

            $rendements = Rendement::getAll($limit, $offset);
            return $this->json($rendements);

        } catch (Exception $e) {
            return $this->error('Erreur lors de la récupération: ' . $e->getMessage());
        }
    }

    /**
     * Calculer le rendement pour un essai
     */
    private function calculate()
    {
        try {
            if (!isset($this->data['essai_id'])) {
                return $this->error('ID de l\'essai requis', 400);
            }

            $essai_id = (int)$this->data['essai_id'];
            $user_id = $this->user['id'];

            // Vérifier si un rendement existe déjà
            $rendement_existant = Rendement::getByEssaiId($essai_id);
            if ($rendement_existant && !isset($this->data['force_recalcul'])) {
                return $this->json([
                    'message' => 'Rendement déjà calculé pour cet essai',
                    'rendement' => $rendement_existant,
                    'recalcul_possible' => true
                ]);
            }

            // Effectuer le calcul
            $resultat = Rendement::calculerRendement($essai_id, $user_id);

            if ($resultat['success']) {
                return $this->json([
                    'message' => 'Rendement calculé avec succès',
                    'rendement_id' => $resultat['rendement_id'],
                    'rendements' => $resultat['rendements'],
                    'donnees_calcul' => $resultat['donnees_calcul']
                ], 201);
            } else {
                return $this->error($resultat['message'], 400);
            }

        } catch (Exception $e) {
            return $this->error('Erreur lors du calcul: ' . $e->getMessage());
        }
    }

    /**
     * Supprimer un rendement
     */
    private function delete()
    {
        try {
            if (!isset($this->data['id'])) {
                return $this->error('ID du rendement requis', 400);
            }

            $id = (int)$this->data['id'];

            if (Rendement::delete($id)) {
                return $this->json(['message' => 'Rendement supprimé avec succès']);
            } else {
                return $this->error('Erreur lors de la suppression', 500);
            }

        } catch (Exception $e) {
            return $this->error('Erreur lors de la suppression: ' . $e->getMessage());
        }
    }

    /**
     * Calculer le rendement en temps réel (pour affichage)
     */
    public function calculateRealTime()
    {
        try {
            if (!isset($this->data['essai_id'])) {
                return $this->error('ID de l\'essai requis', 400);
            }

            $essai_id = (int)$this->data['essai_id'];

            // Récupérer les courbes en temps réel
            $courbe_cpa = Courbe::getByEssaiAndType($essai_id, 'CPA');
            $courbe_cpb = Courbe::getByEssaiAndType($essai_id, 'CPB');

            if (!$courbe_cpa || !$courbe_cpb) {
                return $this->error('Courbes CPA et CPB requises', 400);
            }

            // Calculer les rendements sans sauvegarder
            $donnees_cpa = json_decode($courbe_cpa['donnees'], true);
            $donnees_cpb = json_decode($courbe_cpb['donnees'], true);

            if (!$donnees_cpa || !$donnees_cpb) {
                return $this->error('Données de courbes invalides', 400);
            }

            // Calculs simplifiés pour l'affichage temps réel
            $pression_cpa = $this->calculerMoyennePression($donnees_cpa);
            $pression_cpb = $this->calculerMoyennePression($donnees_cpb);
            $debit = $this->calculerMoyenneDebit($donnees_cpa, $donnees_cpb);

            $rendement_estime = [
                'pression_moyenne_cpa' => round($pression_cpa, 2),
                'pression_moyenne_cpb' => round($pression_cpb, 2),
                'debit_moyen' => round($debit, 2),
                'rendement_estime' => round(($pression_cpb / max($pression_cpa, 1)) * 100, 2)
            ];

            return $this->json([
                'message' => 'Calcul temps réel effectué',
                'donnees' => $rendement_estime,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            return $this->error('Erreur calcul temps réel: ' . $e->getMessage());
        }
    }

    /**
     * Méthodes utilitaires privées
     */
    private function calculerMoyennePression($donnees_courbe)
    {
        if (!is_array($donnees_courbe) || empty($donnees_courbe)) {
            return 0;
        }

        $total = 0;
        $count = 0;

        foreach ($donnees_courbe as $point) {
            if (isset($point['pressure_pascal'])) {
                $total += $point['pressure_pascal'];
                $count++;
            }
        }

        return $count > 0 ? $total / $count : 0;
    }

    private function calculerMoyenneDebit($donnees_cpa, $donnees_cpb)
    {
        $total = 0;
        $count = 0;

        foreach ([$donnees_cpa, $donnees_cpb] as $donnees) {
            if (is_array($donnees)) {
                foreach ($donnees as $point) {
                    if (isset($point['flow_lpm'])) {
                        $total += $point['flow_lpm'];
                        $count++;
                    }
                }
            }
        }

        return $count > 0 ? $total / $count : 0;
    }
}
