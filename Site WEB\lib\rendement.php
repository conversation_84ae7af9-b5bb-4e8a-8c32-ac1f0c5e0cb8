<?php
require_once(__DIR__ . '/../config/database.php');
require_once(__DIR__ . '/courbes.php');
require_once(__DIR__ . '/essais.php');

class Rendement
{
    /**
     * Calculer le rendement hydraulique pour un essai
     * @param int $essai_id ID de l'essai
     * @param int $user_id ID de l'utilisateur qui effectue le calcul
     * @return array Résultat du calcul avec les rendements
     */
    public static function calculerRendement($essai_id, $user_id)
    {
        try {
            // Vérifier que l'essai existe
            $essai = Essai::getById($essai_id);
            if (!$essai) {
                return ['success' => false, 'message' => 'Essai non trouvé'];
            }

            // Récupérer les courbes CPA et CPB
            $courbe_cpa = Courbe::getByEssaiAndType($essai_id, 'CPA');
            $courbe_cpb = Courbe::getByEssaiAndType($essai_id, 'CPB');

            if (!$courbe_cpa || !$courbe_cpb) {
                return ['success' => false, 'message' => 'Courbes CPA et CPB requises pour le calcul'];
            }

            // Décoder les données JSON des courbes
            $donnees_cpa = json_decode($courbe_cpa['donnees'], true);
            $donnees_cpb = json_decode($courbe_cpb['donnees'], true);

            if (!$donnees_cpa || !$donnees_cpb) {
                return ['success' => false, 'message' => 'Données de courbes invalides'];
            }

            // Calculer les moyennes de pression
            $pression_moyenne_cpa = self::calculerMoyennePression($donnees_cpa);
            $pression_moyenne_cpb = self::calculerMoyennePression($donnees_cpb);
            $debit_moyen = self::calculerMoyenneDebit($donnees_cpa, $donnees_cpb);

            // Calculer les rendements
            $rendements = self::calculerRendements(
                $pression_moyenne_cpa,
                $pression_moyenne_cpb,
                $debit_moyen,
                $essai
            );

            // Sauvegarder les résultats
            $rendement_id = self::sauvegarderRendement(
                $essai_id,
                $rendements,
                $pression_moyenne_cpa,
                $pression_moyenne_cpb,
                $debit_moyen,
                $user_id
            );

            if ($rendement_id) {
                return [
                    'success' => true,
                    'rendement_id' => $rendement_id,
                    'rendements' => $rendements,
                    'donnees_calcul' => [
                        'pression_moyenne_cpa' => $pression_moyenne_cpa,
                        'pression_moyenne_cpb' => $pression_moyenne_cpb,
                        'debit_moyen' => $debit_moyen
                    ]
                ];
            } else {
                return ['success' => false, 'message' => 'Erreur lors de la sauvegarde'];
            }

        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Erreur de calcul: ' . $e->getMessage()];
        }
    }

    /**
     * Calculer la moyenne de pression à partir des données de courbe
     */
    private static function calculerMoyennePression($donnees_courbe)
    {
        if (!is_array($donnees_courbe) || empty($donnees_courbe)) {
            return 0;
        }

        $total_pression = 0;
        $count = 0;

        foreach ($donnees_courbe as $point) {
            if (isset($point['pressure_pascal'])) {
                $total_pression += $point['pressure_pascal'];
                $count++;
            }
        }

        return $count > 0 ? $total_pression / $count : 0;
    }

    /**
     * Calculer la moyenne de débit à partir des données de courbes
     */
    private static function calculerMoyenneDebit($donnees_cpa, $donnees_cpb)
    {
        $total_debit = 0;
        $count = 0;

        // Utiliser les données de débit des deux courbes
        foreach ([$donnees_cpa, $donnees_cpb] as $donnees) {
            if (is_array($donnees)) {
                foreach ($donnees as $point) {
                    if (isset($point['flow_lpm'])) {
                        $total_debit += $point['flow_lpm'];
                        $count++;
                    }
                }
            }
        }

        return $count > 0 ? $total_debit / $count : 0;
    }

    /**
     * Calculer les différents types de rendements
     */
    private static function calculerRendements($pression_cpa, $pression_cpb, $debit, $essai)
    {
        // Constantes pour les calculs hydrauliques
        $densite_fluide = 850; // kg/m³ (huile hydraulique typique)
        $gravite = 9.81; // m/s²

        // Conversion des unités
        $debit_m3s = $debit / 60000; // L/min vers m³/s
        $pression_diff = abs($pression_cpa - $pression_cpb); // Différence de pression

        // Puissance hydraulique théorique (W)
        $puissance_hydraulique = $pression_diff * $debit_m3s;

        // Puissance mécanique (estimée à partir des paramètres théoriques)
        $parametre_theorique = json_decode($essai['parametre_theorique'], true);
        $puissance_mecanique_raw = isset($parametre_theorique['puissance'])
            ? $parametre_theorique['puissance']
            : $puissance_hydraulique * 1.2; // Estimation si non fournie

        // Extraire la valeur numérique (enlever les unités comme "W", "kW", etc.)
        $puissance_mecanique = self::extractNumericValue($puissance_mecanique_raw);

        // Validation des valeurs numériques
        $debit = is_numeric($debit) ? (float)$debit : 0;
        $pression_cpa = is_numeric($pression_cpa) ? (float)$pression_cpa : 0;
        $pression_cpb = is_numeric($pression_cpb) ? (float)$pression_cpb : 0;
        $puissance_hydraulique = is_numeric($puissance_hydraulique) ? (float)$puissance_hydraulique : 0;
        $puissance_mecanique = is_numeric($puissance_mecanique) ? (float)$puissance_mecanique : 0;

        // Calcul des rendements (en pourcentage)
        $rendement_volumetrique = $debit > 0 ? min(100, ($debit / ($debit * 1.1)) * 100) : 0;
        $rendement_mecanique = $puissance_mecanique > 0 ? ($puissance_hydraulique / $puissance_mecanique) * 100 : 0;
        $rendement_global = ($rendement_volumetrique * $rendement_mecanique) / 100;

        return [
            'rendement_volumetrique' => round($rendement_volumetrique, 2),
            'rendement_mecanique' => round($rendement_mecanique, 2),
            'rendement_global' => round($rendement_global, 2),
            'puissance_hydraulique' => round($puissance_hydraulique, 2),
            'puissance_mecanique' => round($puissance_mecanique, 2)
        ];
    }

    /**
     * Extraire la valeur numérique d'une chaîne contenant une unité
     */
    private static function extractNumericValue($value)
    {
        if (is_numeric($value)) {
            return max(0, (float)$value);
        }

        if (is_string($value)) {
            // Extraire le nombre au début de la chaîne (entier ou décimal)
            preg_match('/^([0-9]*\.?[0-9]+)/', trim($value), $matches);
            if (isset($matches[1])) {
                return max(0, (float)$matches[1]);
            }
        }

        return 0;
    }

    /**
     * Sauvegarder les résultats de rendement dans la base de données
     */
    private static function sauvegarderRendement($essai_id, $rendements, $pression_cpa, $pression_cpb, $debit, $user_id)
    {
        $stmt = self::getDb()->prepare("
            INSERT INTO rendements (
                essai_id, rendement_volumetrique, rendement_global, rendement_mecanique,
                pression_moyenne_cpa, pression_moyenne_cpb, debit_moyen,
                puissance_hydraulique, puissance_mecanique, calcule_par
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([
            $essai_id,
            $rendements['rendement_volumetrique'],
            $rendements['rendement_global'],
            $rendements['rendement_mecanique'],
            $pression_cpa,
            $pression_cpb,
            $debit,
            $rendements['puissance_hydraulique'],
            $rendements['puissance_mecanique'],
            $user_id
        ]);

        return $result ? self::getDb()->lastInsertId() : false;
    }

    /**
     * Obtenir la connexion à la base de données
     */
    private static function getDb()
    {
        return Database::getInstance()->getConnection();
    }

    /**
     * Récupérer le rendement d'un essai
     */
    public static function getByEssaiId($essai_id)
    {
        $stmt = self::getDb()->prepare("
            SELECT r.*, u.username AS calcule_par_nom
            FROM rendements r
            LEFT JOIN users u ON r.calcule_par = u.id
            WHERE r.essai_id = ?
            ORDER BY r.date_calcul DESC
            LIMIT 1
        ");
        $stmt->execute([$essai_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Récupérer tous les rendements
     */
    public static function getAll($limit = 50, $offset = 0)
    {
        $stmt = self::getDb()->prepare("
            SELECT r.*, e.type AS essai_type, a.numero AS affaire_numero, u.username AS calcule_par_nom
            FROM rendements r
            LEFT JOIN essais e ON r.essai_id = e.id
            LEFT JOIN affaires a ON e.affaire_id = a.id
            LEFT JOIN users u ON r.calcule_par = u.id
            ORDER BY r.date_calcul DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$limit, $offset]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Supprimer un rendement
     */
    public static function delete($id)
    {
        $stmt = self::getDb()->prepare("DELETE FROM rendements WHERE id = ?");
        return $stmt->execute([$id]);
    }

    /**
     * Obtenir les statistiques de rendement pour un affaire
     */
    public static function getStatistiquesAffaire($affaire_id)
    {
        $stmt = self::getDb()->prepare("
            SELECT
                AVG(r.rendement_global) AS rendement_moyen,
                MAX(r.rendement_global) AS rendement_max,
                MIN(r.rendement_global) AS rendement_min,
                COUNT(r.id) AS nombre_calculs
            FROM rendements r
            JOIN essais e ON r.essai_id = e.id
            WHERE e.affaire_id = ?
        ");
        $stmt->execute([$affaire_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Marquer un rendement comme synthétique
     */
    public static function markAsSynthetic($id)
    {
        $stmt = self::getDb()->prepare("UPDATE rendements SET is_synthetic = 1 WHERE id = ?");
        return $stmt->execute([$id]);
    }

    /**
     * Supprimer tous les rendements synthétiques
     */
    public static function deleteSynthetic()
    {
        $stmt = self::getDb()->prepare("DELETE FROM rendements WHERE is_synthetic = 1");
        $stmt->execute();
        return $stmt->rowCount();
    }

    /**
     * Supprimer tous les rendements
     */
    public static function deleteAll()
    {
        $stmt = self::getDb()->prepare("DELETE FROM rendements");
        $stmt->execute();
        $count = $stmt->rowCount();

        // Réinitialiser l'auto-increment
        self::getDb()->exec("ALTER TABLE rendements AUTO_INCREMENT = 1");

        return $count;
    }
}
