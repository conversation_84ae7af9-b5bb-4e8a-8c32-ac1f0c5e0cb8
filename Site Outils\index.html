<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculateur de Vérin Hydraulique</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            height: 100vh;
        }
        .calculator-section {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px;
            width: 30%;
            box-sizing: border-box;
            overflow-y: auto;
        }
        .formulas-section {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px;
            width: 30%;
            box-sizing: border-box;
            overflow-y: auto;
        }
        .input-group {
            margin-bottom: 10px;
        }
        label {
            display: inline-block;
            width: 200px;
            margin-right: 10px;
        }
        input {
            width: 120px;
            padding: 5px;
        }
        .result {
            background-color: #e0e0e0;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="calculator-section">
        <h2>Dimensions et Paramètres de Base</h2>
        <div class="input-group">
            <label>Diamètre du piston (mm):</label>
            <input type="number" id="pistonDiameter" step="any">
        </div>
        <div class="input-group">
            <label>Diamètre de la tige (mm):</label>
            <input type="number" id="rodDiameter" step="any">
        </div>
        <div class="input-group">
            <label>Course (mm):</label>
            <input type="number" id="stroke" step="any">
        </div>
        <div class="input-group">
            <label>Temps de sortie (s):</label>
            <input type="number" id="outTime" step="any">
        </div>
        <div class="input-group">
            <label>Charge (daN):</label>
            <input type="number" id="load" step="any">
        </div>
    </div>

    <div class="calculator-section">
        <h2>Résultats des Calculs</h2>
        <div class="result" id="surfacesResult">
            <h3>Surfaces calculées</h3>
            <p>Surface du fond (Sf): <span id="surfaceFond">-</span> cm²</p>
            <p>Surface de la tige (St): <span id="surfaceTige">-</span> cm²</p>
            <p>Surface annulaire (Sa): <span id="surfaceAnnulaire">-</span> cm²</p>
            <p>Rapport de surface (Sf/St): <span id="surfaceRatio">-</span></p>
        </div>

        <div class="result" id="speedResult">
            <h3>Vitesse et Débits</h3>
            <p>Vitesse: <span id="speed">-</span> m/s</p>
            <p>Débit entrant (côté fond): <span id="debitEntrant">-</span> L/min</p>
            <p>Débit sortant (côté tige): <span id="debitSortant">-</span> L/min</p>
        </div>

        <div class="result" id="pressureResult">
            <h3>Calcul des Pressions</h3>
            <p>Pression due à la charge: <span id="pressureCharge">-</span> bar</p>
            <p>M4 (Retour): <span id="M4">-</span> bar</p>
            <p>M3: <span id="M3">-</span> bar</p>
            <p>M2: <span id="M2">-</span> bar</p>
            <p>M1 (Pression nécessaire): <span id="M1">-</span> bar</p>
            <p>Rendement: <span id="rendement">-</span></p>
        </div>
    </div>

    <div class="formulas-section">
        <h2>Formules</h2>
        <div id="formulasContent">
            <p><strong>Vitesse:</strong> V(m/s) = course (m) / temps (s)</p>
            <p><strong>Débit:</strong> Q (L/min) = 6 * S(cm²) * V(m/s)</p>
            <p><strong>Pression due à la charge:</strong> P = F (daN) / S (cm²)</p>
            <p><strong>Pression nécessaire:</strong> M1 = M2 + ΔP (P vers A)</p>
            <p><strong>Calcul de la pression dans un circuit:</strong></p>
            <ul>
                <li>M4 = perte de charge liée à l'écoulement</li>
                <li>M3 = M4 + perte de charge du distributeur</li>
                <li>M2 = (M3 / rapport de surface du vérin) + pression due à la charge</li>
                <li>M1 = M2 + ΔP (P vers A)</li>
            </ul>
        </div>
    </div>

    <script>
        // Constantes
        const PI = Math.PI;

        // Fonction pour convertir mm² en cm²
        function mm2ToCm2(mm2) {
            return mm2 / 100;
        }

        // Fonction pour convertir cm² en mm²
        function cm2ToMm2(cm2) {
            return cm2 * 100;
        }

        // Calcul des surfaces
        function calculateSurfaces() {
            const pistonDiameter = parseFloat(document.getElementById('pistonDiameter').value);
            const rodDiameter = parseFloat(document.getElementById('rodDiameter').value);
            
            if (pistonDiameter && rodDiameter) {
                const Sf = mm2ToCm2(Math.PI * Math.pow(pistonDiameter/2, 2));
                const St = mm2ToCm2(Math.PI * Math.pow(rodDiameter/2, 2));
                const Sa = Sf - St;
                const ratio = Sf/St;

                document.getElementById('surfaceFond').textContent = Sf.toFixed(2);
                document.getElementById('surfaceTige').textContent = St.toFixed(2);
                document.getElementById('surfaceAnnulaire').textContent = Sa.toFixed(2);
                document.getElementById('surfaceRatio').textContent = ratio.toFixed(2);

                return { Sf, St, Sa, ratio };
            }
            return null;
        }

        // Calcul de la vitesse et des débits
        function calculateSpeedAndFlow() {
            const stroke = parseFloat(document.getElementById('stroke').value);
            const outTime = parseFloat(document.getElementById('outTime').value);
            const speedInput = parseFloat(document.getElementById('speed').textContent);
            
            if (stroke && (outTime || speedInput)) {
                const surfaces = calculateSurfaces();
                if (surfaces) {
                    const speed = speedInput || (stroke/1000) / outTime; // m/s
                    const flowIn = 6 * surfaces.Sf * speed;
                    const flowOut = 6 * surfaces.St * speed;

                    document.getElementById('speed').textContent = speed.toFixed(3);
                    document.getElementById('debitEntrant').textContent = flowIn.toFixed(2);
                    document.getElementById('debitSortant').textContent = flowOut.toFixed(2);

                    return { speed, flowIn, flowOut };
                }
            }
            return null;
        }

        // Calcul des pressions
        function calculatePressures() {
            const load = parseFloat(document.getElementById('load').value);
            const deltaP_PA = parseFloat(document.getElementById('deltaP_PA').value);
            const deltaP_BT = parseFloat(document.getElementById('deltaP_BT').value);
            const deltaP_T = parseFloat(document.getElementById('deltaP_T').value);
            const pressureChargeInput = parseFloat(document.getElementById('pressureCharge').textContent);
            
            const surfaces = calculateSurfaces();
            if (surfaces && (load || pressureChargeInput)) {
                const pressureCharge = pressureChargeInput || load / surfaces.Sf;
                const M4 = deltaP_T;
                const M3 = M4 + deltaP_BT;
                const M2 = (M3 / surfaces.ratio) + pressureCharge;
                const M1 = M2 + deltaP_PA;
                const efficiency = pressureCharge / M1;

                document.getElementById('pressureCharge').textContent = pressureCharge.toFixed(2);
                document.getElementById('M4').textContent = M4.toFixed(2);
                document.getElementById('M3').textContent = M3.toFixed(2);
                document.getElementById('M2').textContent = M2.toFixed(2);
                document.getElementById('M1').textContent = M1.toFixed(2);
                document.getElementById('rendement').textContent = efficiency.toFixed(2);
            }
        }

        // Ajout des événements pour le calcul automatique
        const inputs = document.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                calculateSurfaces();
                calculateSpeedAndFlow();
                calculatePressures();
            });
        });
    </script>
</body>
</html>